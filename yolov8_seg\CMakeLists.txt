# CMakeLists.txt for ONNX Runtime + OpenCV Project
cmake_minimum_required(VERSION 3.16)

# 项目名称和版本
project(yolov8_seg VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译类型（如果没有指定）
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 输出目录设置
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 编译选项
if(MSVC)
    # Windows MSVC 编译器选项
    add_compile_options(/W3)  # 警告级别
    add_compile_options(/MP)  # 多处理器编译
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(NOMINMAX)
else()
    # GCC/Clang 编译器选项
    add_compile_options(-Wall -Wextra)
endif()

# ====================================
# 查找和配置 TensorRT
# ====================================
message(STATUS "查找 TensorRT...")

# 设置 TensorRT 路径（请根据实际路径修改）
set(TENSORRT_ROOT "C:/TensorRT-8.6.1.6")

# 查找头文件
find_path(TENSORRT_INCLUDE_DIR
    NAMES NvInfer.h
    PATHS ${TENSORRT_ROOT}/include
    NO_DEFAULT_PATH
)

# 查找库文件
find_library(TENSORRT_LIB
    NAMES nvinfer
    PATHS ${TENSORRT_ROOT}/lib
    NO_DEFAULT_PATH
)
find_library(TENSORRT_PARSER_LIB
    NAMES nvonnxparser
    PATHS ${TENSORRT_ROOT}/lib
    NO_DEFAULT_PATH
)
find_library(TENSORRT_PLUGIN_LIB
    NAMES nvinfer_plugin
    PATHS ${TENSORRT_ROOT}/lib
    NO_DEFAULT_PATH
)

if(TENSORRT_INCLUDE_DIR AND TENSORRT_LIB)
    message(STATUS "✅ TensorRT 找到")
    message(STATUS "TensorRT 包含目录: ${TENSORRT_INCLUDE_DIR}")
    message(STATUS "TensorRT 库: ${TENSORRT_LIB}")
else()
    message(FATAL_ERROR "❌ TensorRT 未找到")
endif()

# ====================================
# 查找和配置 OpenCV
# ====================================
message(STATUS "查找 OpenCV...")

# 设置OpenCV路径（可根据您的安装路径调整）
if(WIN32)
    set(OpenCV_DIR "D:/opencv/build")
endif()

find_package(OpenCV REQUIRED)

if(OpenCV_FOUND)
    message(STATUS "✅ OpenCV 找到")
    message(STATUS "OpenCV 版本: ${OpenCV_VERSION}")
    message(STATUS "OpenCV 包含目录: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "OpenCV 库: ${OpenCV_LIBS}")
else()
    message(FATAL_ERROR "❌ OpenCV 未找到")
endif()

# ====================================
# 查找和配置 CUDA
# ====================================
message(STATUS "查找 CUDA...")
set(CUDA_TOOLKIT_ROOT_DIR "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8")
set(CUDA_INCLUDE_DIR "${CUDA_TOOLKIT_ROOT_DIR}/include")
set(CUDA_LIB_DIR "${CUDA_TOOLKIT_ROOT_DIR}/lib/x64")

if(EXISTS "${CUDA_INCLUDE_DIR}/cuda_runtime_api.h")
    message(STATUS "✅ CUDA 找到: ${CUDA_INCLUDE_DIR}")
else()
    message(FATAL_ERROR "❌ CUDA 未找到: ${CUDA_INCLUDE_DIR}")
endif()

# ====================================
# 创建可执行文件
# ====================================

add_executable(${PROJECT_NAME} src/main.cpp src/yolov8_seg_trt.cpp)

# 设置包含目录
 target_include_directories(${PROJECT_NAME} PRIVATE
    ${OpenCV_INCLUDE_DIRS}
    ${TENSORRT_INCLUDE_DIR}
    ${CUDA_INCLUDE_DIR}
    ${CMAKE_SOURCE_DIR}/include
)

# 链接库目录
 target_link_directories(${PROJECT_NAME} PRIVATE
    ${CUDA_LIB_DIR}
)

# 链接库
 target_link_libraries(${PROJECT_NAME} PRIVATE
    ${OpenCV_LIBS}
    ${TENSORRT_LIB}
    ${TENSORRT_PARSER_LIB}
    ${TENSORRT_PLUGIN_LIB}
    cudart
)

# ====================================
# 复制DLL文件（Windows）
# ====================================
if(WIN32)
    message(STATUS "配置DLL复制...")
    # OpenCV DLL
    if(EXISTS "${OpenCV_DIR}/x64/vc15/bin")
        set(OPENCV_DLL_DIR "${OpenCV_DIR}/x64/vc15/bin")
    elseif(EXISTS "${OpenCV_DIR}/bin")
        set(OPENCV_DLL_DIR "${OpenCV_DIR}/bin")
    endif()
    # 复制OpenCV DLL
    if(OPENCV_DLL_DIR)
        file(GLOB OPENCV_DLLS "${OPENCV_DLL_DIR}/*.dll")
        foreach(dll ${OPENCV_DLLS})
            add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${dll} $<TARGET_FILE_DIR:${PROJECT_NAME}>
                COMMENT "复制 OpenCV DLL: ${dll}"
            )
        endforeach()
    endif()
    # 复制TensorRT DLL
    set(TENSORRT_DLL_DIR "${TENSORRT_ROOT}/lib")
    file(GLOB TENSORRT_DLLS "${TENSORRT_DLL_DIR}/*.dll")
    foreach(dll ${TENSORRT_DLLS})
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            ${dll} $<TARGET_FILE_DIR:${PROJECT_NAME}>
            COMMENT "复制 TensorRT DLL: ${dll}"
        )
    endforeach()
endif()

# ====================================
# 打印配置信息
# ====================================
message(STATUS "")
message(STATUS "=== 项目配置摘要 ===")
message(STATUS "项目名称: ${PROJECT_NAME}")
message(STATUS "构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++标准: ${CMAKE_CXX_STANDARD}")
message(STATUS "编译器: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "OpenCV版本: ${OpenCV_VERSION}")
message(STATUS "输出目录: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
message(STATUS "CMAKE_SOURCE_DIR 目录: ${CMAKE_SOURCE_DIR}")
